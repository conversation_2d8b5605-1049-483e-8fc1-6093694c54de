import json
import sys
import os
import time
import pickle
import requests
from time import sleep
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException


def extract_mod_info(json_file_path, session, start_from=1, delay=10, preview_only=False):
    """
    Read the JSON file and extract modId and fileId from each mod in the mods array.

    Args:
        json_file_path (str): Path to the collection.json file
        session (requests.Session): Authenticated requests session with cookies
        start_from (int): Mod number to start from (1-based)
        delay (int): Delay in seconds between mod downloads
        preview_only (bool): If True, only show mod info without downloading
    """
    try:
        # Read the JSON file
        with open(json_file_path, 'r', encoding='utf-8') as file:
            data = json.load(file)
        
        # Check if 'mods' key exists
        if 'mods' not in data:
            print("Error: 'mods' key not found in JSON file")
            return
        
        mods = data['mods']
        total_mods = len(mods)
        
        # Validate start_from parameter
        if start_from < 1 or start_from > total_mods:
            print(f"Error: start_from must be between 1 and {total_mods}")
            return
        
        print(f"Found {total_mods} mods in the collection")
        if start_from > 1:
            print(f"Starting from mod #{start_from} (skipping {start_from - 1} mods)")
        print(f"Mode: {'Preview only' if preview_only else 'Download'}")
        print("-" * 60)
        
        # Show collection info if available
        if 'info' in data:
            info = data['info']
            collection_name = info.get('name', 'Unknown Collection')
            author = info.get('author', 'Unknown Author')
            print(f"Collection: {collection_name} by {author}")
            print("-" * 60)
        
        processed_count = 0
        skipped_count = start_from - 1
        
        # Iterate through each mod starting from the specified position
        for i, mod in enumerate(mods, 1):
            if i < start_from:
                continue
                
            mod_name = mod.get('name', 'Unknown')
            mod_version = mod.get('version', 'Unknown')
            mod_author = mod.get('author', 'Unknown')
            
            # Check if source exists and has the required fields
            if 'source' in mod:
                source = mod['source']
                mod_id = source.get('modId', 'N/A')
                file_id = source.get('fileId', 'N/A')
                
                print(f"{i:3d}. {mod_name} v{mod_version}")
                print(f"     Author:  {mod_author}")
                print(f"     modId:   {mod_id}")
                print(f"     fileId:  {file_id}")
                
                if not preview_only and mod_id != 'N/A' and file_id != 'N/A':
                    print(f"     Status:  Fetching download page...")
                    download_key = fetch_download_key(session, mod_id, file_id)
                    if download_key:
                        print(f"     Download key: {download_key}")
                        # TODO: Here you can use the download_key to construct the actual download URL
                        # or save it for later processing
                    processed_count += 1

                    # Show progress
                    remaining = total_mods - i
                    print(f"     Progress: {processed_count} processed, {remaining} remaining")

                    if remaining > 0 and delay > 0:
                        print(f"     Waiting {delay} seconds before next mod...")
                        sleep(delay)
                else:
                    print(f"     Status:  {'Preview mode - not downloading' if preview_only else 'Skipped (missing mod/file ID)'}")
                
                print()
            else:
                print(f"{i:3d}. {mod_name} v{mod_version}")
                print(f"     Author:  {mod_author}")
                print(f"     modId:   N/A (no source)")
                print(f"     fileId:  N/A (no source)")
                print(f"     Status:  Skipped (no source data)")
                print()
        
        # Summary
        print("=" * 60)
        print("SUMMARY:")
        print(f"Total mods in collection: {total_mods}")
        print(f"Mods skipped (start_from): {skipped_count}")
        if not preview_only:
            print(f"Mods processed: {processed_count}")
        print("=" * 60)
    
    except FileNotFoundError:
        print(f"Error: File '{json_file_path}' not found")
    except json.JSONDecodeError as e:
        print(f"Error: Invalid JSON format - {e}")
    except Exception as e:
        print(f"Error: {e}")


def fetch_download_key(session, mod_id, file_id):
    """
    Fetch the download key from NexusMods using authenticated session.

    Args:
        session (requests.Session): Authenticated requests session
        mod_id (int): NexusMods mod ID
        file_id (int): NexusMods file ID

    Returns:
        str: Download key if found, None otherwise
    """
    try:
        # Construct the download page URL
        download_url = f"https://www.nexusmods.com/cyberpunk2077/mods/{mod_id}?tab=files&file_id={file_id}"

        # Set headers to mimic a real browser
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate, br',
            'DNT': '1',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        }

        # Make the request
        response = session.get(download_url, headers=headers)
        response.raise_for_status()

        # TODO: Parse the HTML to extract the download key
        # This is where you'll need to implement the HTML parsing
        # The download key is typically found in JavaScript variables or data attributes

        print(f"     Successfully fetched page (status: {response.status_code})")

        # *** PARSING LOCATION ***
        # You should implement the HTML parsing here to extract the download key
        # Common locations for download keys in NexusMods:
        # 1. JavaScript variables (window.downloadKey, etc.)
        # 2. Data attributes on download buttons
        # 3. Hidden form fields
        # 4. JSON data embedded in script tags

        download_key = parse_download_key_from_html(response.text, mod_id, file_id)
        return download_key

    except requests.RequestException as e:
        print(f"     Error fetching download page: {e}")
        return None
    except Exception as e:
        print(f"     Unexpected error: {e}")
        return None


def parse_download_key_from_html(html_content, mod_id, file_id):
    """
    Parse the download key from the HTML content.

    *** THIS IS WHERE YOU NEED TO IMPLEMENT THE PARSING LOGIC ***

    Args:
        html_content (str): HTML content of the download page
        mod_id (int): Mod ID for reference
        file_id (int): File ID for reference

    Returns:
        str: Download key if found, None otherwise
    """
    # TODO: Uncomment and modify the code below to implement actual parsing
    # You'll need to install BeautifulSoup4: pip install beautifulsoup4

    """
    Example implementation using BeautifulSoup:

    from bs4 import BeautifulSoup
    import re
    import json

    soup = BeautifulSoup(html_content, 'html.parser')

    # Method 1: Look for JavaScript variables
    scripts = soup.find_all('script')
    for script in scripts:
        if script.string:
            # Look for patterns like: var downloadKey = "abc123";
            match = re.search(r'downloadKey\s*=\s*["\']([^"\']+)["\']', script.string)
            if match:
                return match.group(1)

            # Look for patterns like: window.downloadData = {...}
            match = re.search(r'downloadData\s*=\s*({[^}]+})', script.string)
            if match:
                try:
                    data = json.loads(match.group(1))
                    if 'key' in data:
                        return data['key']
                except json.JSONDecodeError:
                    pass

    # Method 2: Look for data attributes on download buttons
    download_elements = soup.find_all(attrs={'data-download-key': True})
    for element in download_elements:
        return element['data-download-key']

    # Method 3: Look for hidden form inputs
    hidden_inputs = soup.find_all('input', {'type': 'hidden', 'name': re.compile(r'.*key.*', re.I)})
    for input_elem in hidden_inputs:
        if input_elem.get('value'):
            return input_elem['value']

    # Method 4: Look for download links with keys in href
    download_links = soup.find_all('a', href=re.compile(r'.*key=([^&]+).*'))
    for link in download_links:
        match = re.search(r'key=([^&]+)', link['href'])
        if match:
            return match.group(1)

    return None
    """

    print(f"     TODO: Parse download key from HTML for mod {mod_id}, file {file_id}")
    print(f"     HTML length: {len(html_content)} characters")
    print(f"     Hint: Look for download buttons, JavaScript variables, or hidden form fields")

    # For debugging, you can save the HTML to a file to inspect it manually:
    # with open(f'debug_mod_{mod_id}_file_{file_id}.html', 'w', encoding='utf-8') as f:
    #     f.write(html_content)
    # print(f"     Debug: HTML saved to debug_mod_{mod_id}_file_{file_id}.html")

    # For now, return None - you'll implement the actual parsing
    return None


def authenticate_with_selenium():
    """
    Use Selenium to authenticate with NexusMods and save cookies.

    Returns:
        requests.Session: Authenticated session with cookies, or None if failed
    """
    cookies_file = "nexusmods_cookies.pkl"

    # Try to load existing cookies first
    session = load_cookies_to_session(cookies_file)
    if session and test_authentication(session):
        print("✓ Using saved authentication cookies")
        return session

    print("=" * 60)
    print("NEXUSMODS AUTHENTICATION")
    print("=" * 60)
    print("Opening browser for NexusMods login...")
    print("Please log in manually in the browser window that opens.")
    print("The script will automatically detect when you're logged in.")

    # Setup Chrome options
    chrome_options = Options()
    chrome_options.add_argument("--disable-blink-features=AutomationControlled")
    chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
    chrome_options.add_experimental_option('useAutomationExtension', False)
    chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")

    driver = None
    try:
        # Initialize the driver
        driver = webdriver.Chrome(options=chrome_options)
        driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")

        # Navigate to NexusMods login page
        driver.get("https://www.nexusmods.com/login")

        # Wait for user to log in (check for profile link or user menu)
        print("Waiting for you to log in...")
        wait = WebDriverWait(driver, 300)  # 5 minutes timeout

        # Wait for login to complete (look for user profile elements)
        try:
            # Wait for either the user menu or profile link to appear
            wait.until(
                lambda d: d.find_element(By.CSS_SELECTOR, ".user-menu, .user-avatar, [href*='/users/']") or
                         "nexusmods.com/users/" in d.current_url
            )
            print("✓ Login detected!")

        except TimeoutException:
            print("✗ Login timeout. Please try again.")
            return None

        # Save cookies
        cookies = driver.get_cookies()
        save_cookies(cookies, cookies_file)
        print("✓ Cookies saved successfully")

        # Create session with cookies
        session = create_session_from_cookies(cookies)

        return session

    except Exception as e:
        print(f"✗ Error during authentication: {e}")
        return None
    finally:
        if driver:
            driver.quit()


def save_cookies(cookies, filename):
    """Save cookies to a pickle file."""
    with open(filename, 'wb') as f:
        pickle.dump(cookies, f)


def load_cookies_to_session(filename):
    """Load cookies from file and create a session."""
    try:
        with open(filename, 'rb') as f:
            cookies = pickle.load(f)
        return create_session_from_cookies(cookies)
    except (FileNotFoundError, pickle.PickleError):
        return None


def create_session_from_cookies(cookies):
    """Create a requests session with the given cookies."""
    session = requests.Session()
    for cookie in cookies:
        session.cookies.set(cookie['name'], cookie['value'], domain=cookie['domain'])
    return session


def test_authentication(session):
    """Test if the session is properly authenticated."""
    try:
        # Try to access a page that requires authentication
        response = session.get("https://www.nexusmods.com/users/myaccount", timeout=10)
        return response.status_code == 200 and "myaccount" in response.url
    except:
        return False


def get_user_inputs():
    """Get user inputs for configuration."""
    print("=" * 60)
    print("NEXUSMODS MOD COLLECTION DOWNLOADER")
    print("=" * 60)

    # Get starting position
    while True:
        try:
            start_input = input("Start from which mod? (Enter number, or press Enter for 1): ").strip()
            if not start_input:
                start_from = 1
                break
            start_from = int(start_input)
            if start_from < 1:
                print("Starting position must be 1 or greater.")
                continue
            break
        except ValueError:
            print("Please enter a valid number.")

    # Get delay
    while True:
        try:
            delay_input = input("Delay between downloads in seconds? (Enter number, or press Enter for 10): ").strip()
            if not delay_input:
                delay = 10
                break
            delay = int(delay_input)
            if delay < 0:
                print("Delay must be 0 or greater.")
                continue
            break
        except ValueError:
            print("Please enter a valid number.")

    # Preview mode
    preview_input = input("Preview mode only? (y/N): ").strip().lower()
    preview_only = preview_input in ['y', 'yes']

    return start_from, delay, preview_only


def main():
    """Main function to run the script."""
    # Default file path
    json_file_path = "collection.json"

    # Allow command line argument for file path
    if len(sys.argv) > 1:
        json_file_path = sys.argv[1]

    # Check if file exists
    if not os.path.exists(json_file_path):
        print(f"Error: File '{json_file_path}' not found")
        print("Please make sure the collection.json file is in the same directory as this script,")
        print("or provide the correct path as a command line argument.")
        return

    # Authenticate with NexusMods
    session = authenticate_with_selenium()
    if not session:
        print("✗ Authentication failed. Cannot proceed.")
        return

    # Get user inputs
    start_from, delay, preview_only = get_user_inputs()

    print(f"\nReading mod information from: {json_file_path}")
    print("=" * 60)

    # Process the mods
    extract_mod_info(json_file_path, session, start_from, delay, preview_only)


if __name__ == "__main__":
    main()
