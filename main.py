import json
import sys
import os
import time
import requests
import re
import gzip
import io
import random
import threading
import logging
from time import sleep
from datetime import datetime, timedelta
from urllib.parse import urljoin, urlparse
from bs4 import BeautifulSoup
import brotli
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('nexusmods_downloader.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Global configuration
CONFIG = {
    'USER_AGENTS': [
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/120.0',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
    ],
    'REQUEST_TIMEOUT': 30,
    'MAX_RETRIES': 3,
    'RETRY_BACKOFF': 2,
    'MIN_DELAY': 5,
    'MAX_DELAY': 15,
    'RATE_LIMIT_DELAY': 60,
    'MAX_CONCURRENT_REQUESTS': 1,  # Conservative for anti-bot
}

class RateLimiter:
    """Rate limiter to prevent overwhelming the server."""
    def __init__(self, min_delay=5, max_delay=15):
        self.min_delay = min_delay
        self.max_delay = max_delay
        self.last_request = 0
        self.request_count = 0
        self.lock = threading.Lock()

    def wait(self):
        """Wait appropriate time before next request."""
        with self.lock:
            current_time = time.time()
            time_since_last = current_time - self.last_request

            # Add randomized delay to appear more human
            base_delay = random.uniform(self.min_delay, self.max_delay)

            # Increase delay if making many requests
            if self.request_count > 10:
                base_delay *= 1.5
            if self.request_count > 20:
                base_delay *= 2

            if time_since_last < base_delay:
                sleep_time = base_delay - time_since_last
                logger.info(f"Rate limiting: waiting {sleep_time:.1f} seconds")
                time.sleep(sleep_time)

            self.last_request = time.time()
            self.request_count += 1

def extract_mod_info(json_file_path, session, start_from=1, delay=10, preview_only=False, rate_limiter=None):
    """
    Read the JSON file and extract modId and fileId from each mod in the mods array.

    Args:
        json_file_path (str): Path to the collection.json file
        session (requests.Session): Authenticated requests session with cookies
        start_from (int): Mod number to start from (1-based)
        delay (int): Delay in seconds between mod downloads
        preview_only (bool): If True, only show mod info without downloading
        rate_limiter (RateLimiter): Rate limiter instance for request throttling
    """
    if rate_limiter is None:
        rate_limiter = RateLimiter(CONFIG['MIN_DELAY'], CONFIG['MAX_DELAY'])
    try:
        # Read the JSON file
        with open(json_file_path, 'r', encoding='utf-8') as file:
            data = json.load(file)
        
        # Check if 'mods' key exists
        if 'mods' not in data:
            print("Error: 'mods' key not found in JSON file")
            return
        
        mods = data['mods']
        total_mods = len(mods)
        
        # Validate start_from parameter
        if start_from < 1 or start_from > total_mods:
            print(f"Error: start_from must be between 1 and {total_mods}")
            return
        
        print(f"Found {total_mods} mods in the collection")
        if start_from > 1:
            print(f"Starting from mod #{start_from} (skipping {start_from - 1} mods)")
        print(f"Mode: {'Preview only' if preview_only else 'Download'}")
        print("-" * 60)
        
        # Show collection info if available
        if 'info' in data:
            info = data['info']
            collection_name = info.get('name', 'Unknown Collection')
            author = info.get('author', 'Unknown Author')
            print(f"Collection: {collection_name} by {author}")
            print("-" * 60)
        
        processed_count = 0
        skipped_count = start_from - 1
        failed_count = 0
        success_count = 0
        failed_mods = []
        start_time = time.time()

        # Create progress tracking
        progress_file = f"progress_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"

        try:
            # Iterate through each mod starting from the specified position
            for i, mod in enumerate(mods, 1):
                if i < start_from:
                    continue

                mod_name = mod.get('name', 'Unknown')
                mod_version = mod.get('version', 'Unknown')
                mod_author = mod.get('author', 'Unknown')

                # Save progress
                save_progress(progress_file, i, total_mods, processed_count, success_count, failed_count)

                # Check if source exists and has the required fields
                if 'source' in mod:
                    source = mod['source']
                    mod_id = source.get('modId', 'N/A')
                    file_id = source.get('fileId', 'N/A')

                    print(f"{i:3d}. {mod_name} v{mod_version}")
                    print(f"     Author:  {mod_author}")
                    print(f"     modId:   {mod_id}")
                    print(f"     fileId:  {file_id}")

                    if not preview_only and mod_id != 'N/A' and file_id != 'N/A':
                        # Apply rate limiting
                        rate_limiter.wait()

                        print(f"     Status:  Fetching download page...")
                        download_url = fetch_download_url(session, mod_id, file_id, rate_limiter)

                        if download_url:
                            print(f"     Download URL: {download_url}")
                            print(f"     Status:  Opening download link...")
                            try:
                                if sys.platform.startswith('win'):
                                    os.startfile(download_url)
                                elif sys.platform.startswith('darwin'):  # macOS
                                    os.system(f'open "{download_url}"')
                                else:  # Linux
                                    os.system(f'xdg-open "{download_url}"')
                                print(f"     ✓ Download link opened successfully")
                                success_count += 1
                                logger.info(f"Successfully processed mod {mod_id}: {mod_name}")
                            except Exception as e:
                                print(f"     ✗ Error opening download link: {e}")
                                failed_count += 1
                                failed_mods.append({
                                    'mod_id': mod_id,
                                    'file_id': file_id,
                                    'name': mod_name,
                                    'version': mod_version,
                                    'author': mod_author,
                                    'reason': f'Error opening download link: {e}'
                                })
                                logger.error(f"Failed to open download link for mod {mod_id}: {e}")
                        else:
                            print(f"     ✗ Could not extract download URL")
                            failed_count += 1
                            failed_mods.append({
                                'mod_id': mod_id,
                                'file_id': file_id,
                                'name': mod_name,
                                'version': mod_version,
                                'author': mod_author,
                                'reason': 'Could not extract download URL'
                            })
                            logger.warning(f"Could not extract download URL for mod {mod_id}: {mod_name}")

                        processed_count += 1

                        # Show progress with ETA
                        remaining = total_mods - i
                        elapsed_time = time.time() - start_time
                        if processed_count > 0:
                            avg_time_per_mod = elapsed_time / processed_count
                            eta_seconds = avg_time_per_mod * remaining
                            eta_str = str(timedelta(seconds=int(eta_seconds)))
                            print(f"     Progress: {processed_count} processed ({success_count} success, {failed_count} failed), {remaining} remaining, ETA: {eta_str}")

                        # Adaptive delay based on success rate
                        if processed_count > 5:
                            success_rate = success_count / processed_count
                            if success_rate < 0.5:  # Less than 50% success
                                adaptive_delay = delay * 2
                                print(f"     Low success rate detected, increasing delay to {adaptive_delay}s")
                                time.sleep(adaptive_delay)
                            elif remaining > 0 and delay > 0:
                                time.sleep(delay)
                        elif remaining > 0 and delay > 0:
                            time.sleep(delay)
                    else:
                        print(f"     Status:  {'Preview mode - not downloading' if preview_only else 'Skipped (missing mod/file ID)'}")

                    print()
                else:
                    print(f"{i:3d}. {mod_name} v{mod_version}")
                    print(f"     Author:  {mod_author}")
                    print(f"     modId:   N/A (no source)")
                    print(f"     fileId:  N/A (no source)")
                    print(f"     Status:  Skipped (no source data)")
                    print()

        except KeyboardInterrupt:
            print("\n⚠ Process interrupted by user")
            logger.info("Process interrupted by user")
        except Exception as e:
            print(f"\n✗ Unexpected error: {e}")
            logger.error(f"Unexpected error during processing: {e}")
        finally:
            # Clean up progress file
            try:
                os.remove(progress_file)
            except:
                pass
        
        # Enhanced Summary
        total_time = time.time() - start_time
        print("=" * 60)
        print("FINAL SUMMARY:")
        print(f"Total mods in collection: {total_mods}")
        print(f"Mods skipped (start_from): {skipped_count}")
        if not preview_only:
            print(f"Mods processed: {processed_count}")
            print(f"Successful downloads: {success_count}")
            print(f"Failed downloads: {failed_count}")
            if processed_count > 0:
                success_rate = (success_count / processed_count) * 100
                print(f"Success rate: {success_rate:.1f}%")
            print(f"Total time: {str(timedelta(seconds=int(total_time)))}")
            if processed_count > 0:
                avg_time = total_time / processed_count
                print(f"Average time per mod: {avg_time:.1f}s")
        print("=" * 60)

        # Log final summary
        logger.info(f"Session completed: {success_count}/{processed_count} successful downloads in {total_time:.1f}s")

        return failed_mods
    
    except FileNotFoundError:
        print(f"Error: File '{json_file_path}' not found")
    except json.JSONDecodeError as e:
        print(f"Error: Invalid JSON format - {e}")
    except Exception as e:
        print(f"Error: {e}")


def save_progress(filename, current_mod, total_mods, processed, success, failed):
    """Save current progress to a file."""
    try:
        progress_data = {
            'timestamp': datetime.now().isoformat(),
            'current_mod': current_mod,
            'total_mods': total_mods,
            'processed': processed,
            'success': success,
            'failed': failed,
            'percentage': (current_mod / total_mods) * 100
        }
        with open(filename, 'w') as f:
            json.dump(progress_data, f, indent=2)
    except Exception as e:
        logger.warning(f"Could not save progress: {e}")


def create_enhanced_session():
    """Create a requests session with enhanced anti-bot measures."""
    session = requests.Session()

    # Configure retry strategy
    retry_strategy = Retry(
        total=CONFIG['MAX_RETRIES'],
        backoff_factor=CONFIG['RETRY_BACKOFF'],
        status_forcelist=[429, 500, 502, 503, 504],
        allowed_methods=["HEAD", "GET", "OPTIONS"]
    )

    # Mount adapter with retry strategy
    adapter = HTTPAdapter(max_retries=retry_strategy)
    session.mount("http://", adapter)
    session.mount("https://", adapter)

    # Set default timeout
    session.timeout = CONFIG['REQUEST_TIMEOUT']

    return session


def get_random_headers():
    """Generate randomized headers to appear more human."""
    user_agent = random.choice(CONFIG['USER_AGENTS'])

    headers = {
        'User-Agent': user_agent,
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
        'Accept-Language': random.choice([
            'en-US,en;q=0.9',
            'en-US,en;q=0.8,es;q=0.7',
            'en-GB,en;q=0.9',
            'en-US,en;q=0.5'
        ]),
        'Accept-Encoding': 'gzip, deflate, br',
        'DNT': '1',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
        'Sec-Fetch-Dest': 'document',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'none',
        'Sec-Fetch-User': '?1',
        'Cache-Control': random.choice(['no-cache', 'max-age=0']),
        'Pragma': 'no-cache',
    }

    # Randomly add some optional headers
    if random.random() > 0.5:
        headers['Sec-CH-UA'] = '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"'
        headers['Sec-CH-UA-Mobile'] = '?0'
        headers['Sec-CH-UA-Platform'] = '"Windows"'

    return headers


def fetch_download_url(session, mod_id, file_id, rate_limiter=None):
    """
    Fetch the download URL from NexusMods using authenticated session with enhanced anti-bot measures.

    Args:
        session (requests.Session): Authenticated requests session
        mod_id (int): NexusMods mod ID
        file_id (int): NexusMods file ID
        rate_limiter (RateLimiter): Rate limiter for request throttling

    Returns:
        str: NXM download URL if found, None otherwise
    """
    if rate_limiter is None:
        rate_limiter = RateLimiter()

    max_attempts = 3
    for attempt in range(max_attempts):
        try:
            # Construct the download page URL with randomized parameters
            base_url = f"https://www.nexusmods.com/cyberpunk2077/mods/{mod_id}"
            params = {
                'tab': 'files',
                'file_id': file_id,
                'nmm': '1',
                'mtm_source': 'nexusmodsapp',
                'mtm_campaign': 'collections',
                '_t': int(time.time())  # Cache busting
            }

            # Get randomized headers
            headers = get_random_headers()

            # Add referer to appear more legitimate
            headers['Referer'] = f"https://www.nexusmods.com/cyberpunk2077/mods/{mod_id}"

            # Make the request with timeout and stream
            logger.debug(f"Fetching download page for mod {mod_id}, file {file_id} (attempt {attempt + 1})")
            response = session.get(base_url, params=params, headers=headers,
                                 timeout=CONFIG['REQUEST_TIMEOUT'], stream=True)

            # Check for rate limiting
            if response.status_code == 429:
                retry_after = int(response.headers.get('Retry-After', CONFIG['RATE_LIMIT_DELAY']))
                print(f"     Rate limited, waiting {retry_after} seconds...")
                logger.warning(f"Rate limited for mod {mod_id}, waiting {retry_after}s")
                time.sleep(retry_after)
                continue

            response.raise_for_status()

            print(f"     Successfully fetched page (status: {response.status_code})")
            print(f"     Content-Type: {response.headers.get('content-type', 'unknown')}")
            print(f"     Content-Encoding: {response.headers.get('content-encoding', 'none')}")

            # Log response details for debugging
            logger.debug(f"Response for mod {mod_id}: status={response.status_code}, "
                        f"content-type={response.headers.get('content-type')}, "
                        f"content-length={response.headers.get('content-length')}")

            # Enhanced content decompression with multiple fallbacks
            html_content = None
            content_encoding = response.headers.get('content-encoding', '').lower()

            try:
                if content_encoding == 'br':
                    # Brotli compression
                    compressed = response.content
                    html_content = brotli.decompress(compressed).decode('utf-8')
                    logger.debug(f"Successfully decompressed brotli content for mod {mod_id}")
                elif content_encoding == 'gzip':
                    # Gzip compression
                    html_content = gzip.decompress(response.content).decode('utf-8')
                    logger.debug(f"Successfully decompressed gzip content for mod {mod_id}")
                else:
                    # Try response.text first (handles most cases automatically)
                    html_content = response.text

                    # Fallback: if content looks binary, try manual decompression
                    if len(html_content) > 0 and ord(html_content[0]) < 32 and html_content[0] not in '\t\n\r':
                        logger.debug(f"Content appears binary for mod {mod_id}, trying manual decompression")
                        try:
                            html_content = brotli.decompress(response.content).decode('utf-8')
                        except:
                            try:
                                html_content = gzip.decompress(response.content).decode('utf-8')
                            except:
                                html_content = response.content.decode('utf-8', errors='replace')

                print(f"     HTML content length: {len(html_content)} characters")

                # Verify content looks like HTML
                if '<html' in html_content.lower() or '<!doctype' in html_content.lower():
                    print(f"     ✓ Content appears to be valid HTML")
                    logger.debug(f"Valid HTML content received for mod {mod_id}")
                else:
                    print(f"     ⚠ Content may not be valid HTML (first 100 chars): {html_content[:100]}")
                    logger.warning(f"Suspicious content for mod {mod_id}: {html_content[:100]}")

            except Exception as decode_error:
                logger.error(f"Content decoding failed for mod {mod_id}: {decode_error}")
                print(f"     Error decoding content: {decode_error}")
                # Final fallback
                try:
                    html_content = response.content.decode('utf-8', errors='replace')
                    print(f"     Using fallback decoding, length: {len(html_content)} characters")
                except Exception as final_error:
                    logger.error(f"Complete decoding failure for mod {mod_id}: {final_error}")
                    print(f"     Complete failure to decode content: {final_error}")
                    if attempt < max_attempts - 1:
                        print(f"     Retrying in {2 ** attempt} seconds...")
                        time.sleep(2 ** attempt)
                        continue
                    return None

            # Parse the HTML to extract the download URL
            download_url_result = parse_download_url_from_html(html_content, mod_id, file_id)
            if download_url_result:
                logger.info(f"Successfully extracted download URL for mod {mod_id}")
                return download_url_result
            elif attempt < max_attempts - 1:
                print(f"     No download URL found, retrying in {2 ** attempt} seconds...")
                logger.warning(f"No download URL found for mod {mod_id}, attempt {attempt + 1}")
                time.sleep(2 ** attempt)
                continue
            else:
                logger.error(f"Failed to extract download URL for mod {mod_id} after {max_attempts} attempts")
                return None

        except requests.exceptions.Timeout:
            logger.warning(f"Timeout for mod {mod_id}, attempt {attempt + 1}")
            print(f"     Request timeout (attempt {attempt + 1})")
            if attempt < max_attempts - 1:
                time.sleep(2 ** attempt)
                continue
        except requests.exceptions.RequestException as e:
            logger.error(f"Request error for mod {mod_id}, attempt {attempt + 1}: {e}")
            print(f"     Request error: {e}")
            if attempt < max_attempts - 1:
                time.sleep(2 ** attempt)
                continue
        except Exception as e:
            logger.error(f"Unexpected error for mod {mod_id}, attempt {attempt + 1}: {e}")
            print(f"     Unexpected error: {e}")
            if attempt < max_attempts - 1:
                time.sleep(2 ** attempt)
                continue

    logger.error(f"All attempts failed for mod {mod_id}")
    return None


def parse_download_url_from_html(html_content, mod_id, file_id):
    """
    Parse the download URL from the HTML content by looking for the slowDownloadButton.

    Args:
        html_content (str): HTML content of the download page
        mod_id (int): Mod ID for reference
        file_id (int): File ID for reference

    Returns:
        str: NXM download URL if found, None otherwise
    """
    try:
        # Check if we have valid HTML content
        if not html_content or len(html_content.strip()) < 100:
            print(f"     ✗ HTML content is too short or empty (length: {len(html_content) if html_content else 0})")
            return None

        soup = BeautifulSoup(html_content, 'html.parser')

        # Look for the slowDownloadButton with data-download-url attribute
        slow_download_button = soup.find('button', id='slowDownloadButton')
        if slow_download_button:
            print(f"     ✓ Found slowDownloadButton element")
            if slow_download_button.get('data-download-url'):
                download_url = slow_download_button['data-download-url']
                print(f"     ✓ Found download URL in slowDownloadButton")
                return download_url
            else:
                print(f"     ✗ slowDownloadButton found but no data-download-url attribute")
                print(f"     Button attributes: {slow_download_button.attrs}")
        else:
            print(f"     ✗ slowDownloadButton not found")

        # Alternative: Look for any element with data-download-url attribute
        download_elements = soup.find_all(attrs={'data-download-url': True})
        print(f"     Found {len(download_elements)} elements with data-download-url attribute")
        for element in download_elements:
            download_url = element['data-download-url']
            print(f"     ✓ Found download URL in element: {element.name} (id: {element.get('id', 'none')})")
            return download_url

        # Alternative: Look for NXM links in any attributes or text
        # Search for nxm:// URLs in the entire HTML
        nxm_pattern = r'nxm://[^"\'\s<>]+'
        nxm_matches = re.findall(nxm_pattern, html_content)
        print(f"     Found {len(nxm_matches)} NXM URLs in HTML content")

        if nxm_matches:
            # Filter for cyberpunk2077 and matching mod/file IDs if possible
            for nxm_url in nxm_matches:
                if 'cyberpunk2077' in nxm_url and str(mod_id) in nxm_url and str(file_id) in nxm_url:
                    print(f"     ✓ Found matching NXM URL in HTML content: {nxm_url[:100]}...")
                    return nxm_url
            # If no exact match, return the first cyberpunk2077 NXM URL
            for nxm_url in nxm_matches:
                if 'cyberpunk2077' in nxm_url:
                    print(f"     ✓ Found Cyberpunk 2077 NXM URL in HTML content: {nxm_url[:100]}...")
                    return nxm_url
            # If no cyberpunk2077 URLs, show what we found
            print(f"     Found NXM URLs but none for cyberpunk2077:")
            for i, url in enumerate(nxm_matches[:3]):  # Show first 3
                print(f"       {i+1}. {url[:100]}...")

        # Look for download buttons or links that might contain the URL
        download_buttons = soup.find_all(['button', 'a'], class_=re.compile(r'download', re.I))
        print(f"     Found {len(download_buttons)} potential download buttons/links")
        for button in download_buttons[:5]:  # Check first 5
            for attr in ['data-download-url', 'href', 'data-url', 'data-link']:
                if button.get(attr) and 'nxm://' in str(button.get(attr)):
                    print(f"     ✓ Found NXM URL in {button.name} {attr}: {button[attr][:100]}...")
                    return button[attr]

        print(f"     ✗ No download URL found for mod {mod_id}, file {file_id}")

        # For debugging, save the HTML and analysis to inspect manually
        save_debug_files(html_content, mod_id, file_id, soup)

        return None

    except Exception as e:
        print(f"     ✗ Error parsing HTML: {e}")
        return None


def save_debug_files(html_content, mod_id, file_id, soup=None):
    """Save debug files for manual inspection."""
    base_filename = f'debug_mod_{mod_id}_file_{file_id}'

    # Save the raw HTML
    html_filename = f'{base_filename}.html'
    try:
        # Ensure we can write the content as readable text
        if isinstance(html_content, bytes):
            try:
                html_text = html_content.decode('utf-8')
            except UnicodeDecodeError:
                html_text = html_content.decode('utf-8', errors='replace')
        else:
            html_text = html_content

        # Double-check if content is still binary-looking
        if len(html_text) > 0 and ord(html_text[0]) < 32 and html_text[0] not in '\t\n\r':
            print(f"     Debug: Content still appears binary, attempting decompression...")
            import gzip
            import io
            try:
                # If html_content is a string but looks binary, it might be incorrectly decoded
                if isinstance(html_content, str):
                    # Convert back to bytes and try decompression
                    binary_content = html_content.encode('latin1')  # Preserve byte values
                else:
                    binary_content = html_content

                html_text = gzip.decompress(binary_content).decode('utf-8')
                print(f"     Debug: Successfully decompressed content for saving")
            except Exception as decomp_error:
                print(f"     Debug: Decompression failed: {decomp_error}")
                # Use the original text with replacements for unreadable chars
                html_text = ''.join(c if ord(c) >= 32 or c in '\t\n\r' else f'[BYTE:{ord(c)}]' for c in html_text[:10000])
                html_text += "\n\n<!-- Content was binary/compressed and may not be fully readable -->"

        with open(html_filename, 'w', encoding='utf-8') as f:
            f.write(html_text)
        print(f"     Debug: HTML saved to {html_filename}")

        # Verify the saved file is readable
        with open(html_filename, 'r', encoding='utf-8') as f:
            test_content = f.read(100)
            if '<html' in test_content.lower() or '<!doctype' in test_content.lower():
                print(f"     Debug: ✓ Saved HTML file appears readable")
            else:
                print(f"     Debug: ⚠ Saved HTML file may still have issues")

    except Exception as e:
        print(f"     Debug: Could not save HTML file: {e}")
        # Try saving as binary with a note
        try:
            with open(f'{base_filename}.bin', 'wb') as f:
                if isinstance(html_content, str):
                    f.write(html_content.encode('utf-8', errors='replace'))
                else:
                    f.write(html_content)

            # Also create a note file
            with open(f'{base_filename}_note.txt', 'w', encoding='utf-8') as f:
                f.write("The HTML content was saved as binary because it could not be properly decoded.\n")
                f.write("This usually means the content is compressed or in an unexpected format.\n")
                f.write(f"File size: {len(html_content)} bytes\n")
                f.write(f"Content type: {type(html_content)}\n")

            print(f"     Debug: Binary content saved to {base_filename}.bin with note")
        except Exception as e2:
            print(f"     Debug: Could not save binary file either: {e2}")

    # Save analysis file
    analysis_filename = f'{base_filename}_analysis.txt'
    try:
        with open(analysis_filename, 'w', encoding='utf-8') as f:
            f.write(f"Debug Analysis for Mod {mod_id}, File {file_id}\n")
            f.write("=" * 50 + "\n\n")

            f.write(f"HTML Content Length: {len(html_content) if html_content else 0}\n")
            f.write(f"Content Type: {type(html_content)}\n\n")

            if soup:
                # Find all buttons
                buttons = soup.find_all('button')
                f.write(f"Total buttons found: {len(buttons)}\n")
                for i, button in enumerate(buttons[:10]):  # First 10 buttons
                    f.write(f"  Button {i+1}: id='{button.get('id', 'none')}', class='{button.get('class', 'none')}'\n")
                    if button.get('data-download-url'):
                        f.write(f"    data-download-url: {button['data-download-url']}\n")
                f.write("\n")

                # Find all elements with data-download-url
                download_elements = soup.find_all(attrs={'data-download-url': True})
                f.write(f"Elements with data-download-url: {len(download_elements)}\n")
                for elem in download_elements:
                    f.write(f"  {elem.name}: {elem.get('data-download-url')}\n")
                f.write("\n")

                # Search for NXM URLs
                nxm_pattern = r'nxm://[^"\'\s<>]+'
                nxm_matches = re.findall(nxm_pattern, str(soup))
                f.write(f"NXM URLs found: {len(nxm_matches)}\n")
                for url in nxm_matches:
                    f.write(f"  {url}\n")

        print(f"     Debug: Analysis saved to {analysis_filename}")
    except Exception as e:
        print(f"     Debug: Could not save analysis file: {e}")


def get_cookies_from_user():
    """
    Get cookies from user input and create an authenticated session with enhanced security.

    Returns:
        requests.Session: Authenticated session with cookies, or None if failed
    """
    cookies_file = "nexusmods_cookies.json"

    # Try to load existing cookies first
    session = load_cookies_from_file(cookies_file)
    if session and test_authentication(session):
        print("✓ Using saved authentication cookies")
        logger.info("Successfully loaded saved authentication cookies")
        return enhance_session_security(session)

    print("=" * 60)
    print("NEXUSMODS COOKIE AUTHENTICATION")
    print("=" * 60)
    print("To avoid bot detection, please provide your NexusMods cookies manually.")
    print()
    print("How to get your cookies:")
    print("1. Open your browser and go to https://www.nexusmods.com")
    print("2. Log in to your account")
    print("3. Open Developer Tools (F12)")
    print("4. Go to Application/Storage tab > Cookies > https://www.nexusmods.com")
    print("5. Find and copy the following cookie values:")
    print()

    # Get essential cookies from user
    cookies = {}

    # Get session cookie (most important)
    while True:
        session_cookie = input("Enter your 'nexusmods_session' cookie value: ").strip()
        if session_cookie:
            cookies['nexusmods_session'] = session_cookie
            break
        print("Session cookie is required. Please try again.")

    # Get CSRF token (often needed)
    csrf_token = input("Enter your 'csrf_token' cookie value (or press Enter to skip): ").strip()
    if csrf_token:
        cookies['csrf_token'] = csrf_token

    # Get any additional cookies
    print("\nOptional: Enter any additional cookies (format: name=value, press Enter when done):")
    while True:
        additional_cookie = input("Additional cookie (or Enter to finish): ").strip()
        if not additional_cookie:
            break
        if '=' in additional_cookie:
            name, value = additional_cookie.split('=', 1)
            cookies[name.strip()] = value.strip()
            print(f"Added cookie: {name.strip()}")
        else:
            print("Invalid format. Use: name=value")

    # Create enhanced session with cookies
    session = create_session_from_cookies_dict(cookies)
    session = enhance_session_security(session)

    # Test authentication with retry
    auth_attempts = 3
    for attempt in range(auth_attempts):
        if test_authentication(session):
            print("✓ Authentication successful!")
            logger.info("Cookie authentication successful")
            save_cookies_to_file(cookies, cookies_file)
            print("✓ Cookies saved for future use")
            return session
        elif attempt < auth_attempts - 1:
            print(f"Authentication failed, retrying... (attempt {attempt + 2}/{auth_attempts})")
            time.sleep(2)

    print("✗ Authentication failed after multiple attempts. Please check your cookies and try again.")
    logger.error("Cookie authentication failed after multiple attempts")
    return None


def enhance_session_security(session):
    """Enhance session with additional security measures."""
    # Configure session with enhanced settings
    session.headers.update({
        'DNT': '1',
        'Sec-Fetch-Dest': 'document',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'same-origin',
        'Sec-Fetch-User': '?1',
    })

    # Configure retry strategy
    retry_strategy = Retry(
        total=CONFIG['MAX_RETRIES'],
        backoff_factor=CONFIG['RETRY_BACKOFF'],
        status_forcelist=[429, 500, 502, 503, 504],
        allowed_methods=["HEAD", "GET", "OPTIONS"]
    )

    adapter = HTTPAdapter(max_retries=retry_strategy)
    session.mount("http://", adapter)
    session.mount("https://", adapter)

    return session


def save_cookies_to_file(cookies_dict, filename):
    """Save cookies dictionary to a JSON file."""
    try:
        with open(filename, 'w') as f:
            json.dump(cookies_dict, f, indent=2)
    except Exception as e:
        print(f"Warning: Could not save cookies: {e}")


def load_cookies_from_file(filename):
    """Load cookies from JSON file and create a session."""
    try:
        with open(filename, 'r') as f:
            cookies_dict = json.load(f)
        return create_session_from_cookies_dict(cookies_dict)
    except (FileNotFoundError, json.JSONDecodeError):
        return None


def create_session_from_cookies_dict(cookies_dict):
    """Create a requests session with the given cookies dictionary and enhanced settings."""
    session = create_enhanced_session()

    # Set cookies with proper domain and security settings
    for name, value in cookies_dict.items():
        session.cookies.set(
            name=name,
            value=value,
            domain='.nexusmods.com',
            secure=True,
            rest={'HttpOnly': True}
        )

    # Add common headers
    session.headers.update(get_random_headers())

    return session


def test_authentication(session):
    """Test if the session is properly authenticated with enhanced validation."""
    try:
        # Test multiple endpoints to ensure authentication is working
        test_urls = [
            "https://www.nexusmods.com/users/myaccount",
            "https://www.nexusmods.com/cyberpunk2077/users/myaccount"
        ]

        for url in test_urls:
            try:
                headers = get_random_headers()
                response = session.get(url, timeout=15, headers=headers)

                if response.status_code == 200:
                    # Check for authentication indicators
                    content = response.text.lower()
                    auth_indicators = ['myaccount', 'logout', 'profile', 'premium']

                    if any(indicator in content for indicator in auth_indicators):
                        logger.info(f"Authentication verified via {url}")
                        return True

            except requests.RequestException as e:
                logger.debug(f"Auth test failed for {url}: {e}")
                continue

        logger.warning("Authentication test failed for all endpoints")
        return False

    except Exception as e:
        logger.error(f"Authentication test error: {e}")
        return False


def get_user_inputs():
    """Get user inputs for configuration with enhanced options."""
    print("=" * 60)
    print("NEXUSMODS MOD COLLECTION DOWNLOADER - ENHANCED")
    print("=" * 60)

    # Get starting position
    while True:
        try:
            start_input = input("Start from which mod? (Enter number, or press Enter for 1): ").strip()
            if not start_input:
                start_from = 1
                break
            start_from = int(start_input)
            if start_from < 1:
                print("Starting position must be 1 or greater.")
                continue
            break
        except ValueError:
            print("Please enter a valid number.")

    # Get delay with intelligent defaults
    while True:
        try:
            delay_input = input("Delay between downloads in seconds? (Enter number, or press Enter for smart delay): ").strip()
            if not delay_input:
                delay = random.randint(CONFIG['MIN_DELAY'], CONFIG['MAX_DELAY'])
                print(f"Using smart delay: {delay} seconds (randomized between {CONFIG['MIN_DELAY']}-{CONFIG['MAX_DELAY']}s)")
                break
            delay = int(delay_input)
            if delay < 0:
                print("Delay must be 0 or greater.")
                continue
            break
        except ValueError:
            print("Please enter a valid number.")

    # Preview mode
    preview_input = input("Preview mode only? (y/N): ").strip().lower()
    preview_only = preview_input in ['y', 'yes']

    # Advanced options
    print("\nAdvanced Options:")

    # Verbose logging
    verbose_input = input("Enable verbose logging? (y/N): ").strip().lower()
    verbose = verbose_input in ['y', 'yes']
    if verbose:
        logging.getLogger().setLevel(logging.DEBUG)
        print("✓ Verbose logging enabled")

    # Retry failed downloads
    retry_input = input("Retry failed downloads at the end? (Y/n): ").strip().lower()
    retry_failed = retry_input not in ['n', 'no']

    # Save failed mods list
    save_failed_input = input("Save list of failed mods to file? (Y/n): ").strip().lower()
    save_failed = save_failed_input not in ['n', 'no']

    return {
        'start_from': start_from,
        'delay': delay,
        'preview_only': preview_only,
        'verbose': verbose,
        'retry_failed': retry_failed,
        'save_failed': save_failed
    }


def main():
    """Enhanced main function with comprehensive error handling and features."""
    try:
        # Setup logging
        logger.info("Starting NexusMods Collection Downloader")

        # Default file path
        json_file_path = "collection.json"

        # Allow command line argument for file path
        if len(sys.argv) > 1:
            json_file_path = sys.argv[1]

        # Check if file exists
        if not os.path.exists(json_file_path):
            print(f"Error: File '{json_file_path}' not found")
            print("Please make sure the collection.json file is in the same directory as this script,")
            print("or provide the correct path as a command line argument.")
            logger.error(f"Collection file not found: {json_file_path}")
            return

        # Validate JSON file
        try:
            with open(json_file_path, 'r', encoding='utf-8') as f:
                collection_data = json.load(f)
            if 'mods' not in collection_data:
                print("Error: Invalid collection file - 'mods' key not found")
                logger.error("Invalid collection file format")
                return
            print(f"✓ Collection file validated: {len(collection_data['mods'])} mods found")
        except json.JSONDecodeError as e:
            print(f"Error: Invalid JSON format in collection file: {e}")
            logger.error(f"JSON decode error: {e}")
            return

        # Authenticate with NexusMods using cookies
        print("\nStep 1: Authentication")
        session = get_cookies_from_user()
        if not session:
            print("✗ Authentication failed. Cannot proceed.")
            logger.error("Authentication failed")
            return

        # Get user inputs
        print("\nStep 2: Configuration")
        config = get_user_inputs()

        # Create rate limiter
        rate_limiter = RateLimiter(CONFIG['MIN_DELAY'], CONFIG['MAX_DELAY'])

        print(f"\nStep 3: Processing")
        print(f"Reading mod information from: {json_file_path}")
        print("=" * 60)

        # Process the mods
        failed_mods = extract_mod_info(
            json_file_path,
            session,
            config['start_from'],
            config['delay'],
            config['preview_only'],
            rate_limiter
        )

        # Handle failed mods if requested
        if config['retry_failed'] and failed_mods and not config['preview_only']:
            print(f"\nRetrying {len(failed_mods)} failed mods...")
            logger.info(f"Retrying {len(failed_mods)} failed mods")
            # Implement retry logic here if needed

        # Save failed mods list if requested
        if config['save_failed'] and failed_mods:
            failed_file = f"failed_mods_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            try:
                with open(failed_file, 'w') as f:
                    json.dump(failed_mods, f, indent=2)
                print(f"✓ Failed mods list saved to: {failed_file}")
                logger.info(f"Failed mods list saved to {failed_file}")
            except Exception as e:
                print(f"⚠ Could not save failed mods list: {e}")
                logger.warning(f"Could not save failed mods list: {e}")

        print("\n🎉 Process completed successfully!")
        logger.info("Process completed successfully")

    except KeyboardInterrupt:
        print("\n⚠ Process interrupted by user")
        logger.info("Process interrupted by user")
    except Exception as e:
        print(f"\n✗ Unexpected error: {e}")
        logger.error(f"Unexpected error in main: {e}", exc_info=True)
        raise


if __name__ == "__main__":
    main()
