import json
import sys
import os
import time
from time import sleep


def extract_mod_info(json_file_path, user_id, api_key, start_from=1, delay=10, preview_only=False):
    """
    Read the JSON file and extract modId and fileId from each mod in the mods array.
    
    Args:
        json_file_path (str): Path to the collection.json file
        user_id (str): NexusMods user ID
        api_key (str): NexusMods API key
        start_from (int): Mod number to start from (1-based)
        delay (int): Delay in seconds between mod downloads
        preview_only (bool): If True, only show mod info without downloading
    """
    try:
        # Read the JSON file
        with open(json_file_path, 'r', encoding='utf-8') as file:
            data = json.load(file)
        
        # Check if 'mods' key exists
        if 'mods' not in data:
            print("Error: 'mods' key not found in JSON file")
            return
        
        mods = data['mods']
        total_mods = len(mods)
        
        # Validate start_from parameter
        if start_from < 1 or start_from > total_mods:
            print(f"Error: start_from must be between 1 and {total_mods}")
            return
        
        print(f"Found {total_mods} mods in the collection")
        if start_from > 1:
            print(f"Starting from mod #{start_from} (skipping {start_from - 1} mods)")
        print(f"Mode: {'Preview only' if preview_only else 'Download'}")
        print("-" * 60)
        
        # Show collection info if available
        if 'info' in data:
            info = data['info']
            collection_name = info.get('name', 'Unknown Collection')
            author = info.get('author', 'Unknown Author')
            print(f"Collection: {collection_name} by {author}")
            print("-" * 60)
        
        processed_count = 0
        skipped_count = start_from - 1
        
        # Iterate through each mod starting from the specified position
        for i, mod in enumerate(mods, 1):
            if i < start_from:
                continue
                
            mod_name = mod.get('name', 'Unknown')
            mod_version = mod.get('version', 'Unknown')
            mod_author = mod.get('author', 'Unknown')
            
            # Check if source exists and has the required fields
            if 'source' in mod:
                source = mod['source']
                mod_id = source.get('modId', 'N/A')
                file_id = source.get('fileId', 'N/A')
                
                print(f"{i:3d}. {mod_name} v{mod_version}")
                print(f"     Author:  {mod_author}")
                print(f"     modId:   {mod_id}")
                print(f"     fileId:  {file_id}")
                
                if not preview_only and mod_id != 'N/A' and file_id != 'N/A':
                    print(f"     Status:  Opening download link...")
                    run_requests(mod_id, file_id, user_id, api_key)
                    processed_count += 1
                    
                    # Show progress
                    remaining = total_mods - i
                    print(f"     Progress: {processed_count} processed, {remaining} remaining")
                    
                    if remaining > 0 and delay > 0:
                        print(f"     Waiting {delay} seconds before next mod...")
                        sleep(delay)
                else:
                    print(f"     Status:  {'Preview mode - not downloading' if preview_only else 'Skipped (missing mod/file ID)'}")
                
                print()
            else:
                print(f"{i:3d}. {mod_name} v{mod_version}")
                print(f"     Author:  {mod_author}")
                print(f"     modId:   N/A (no source)")
                print(f"     fileId:  N/A (no source)")
                print(f"     Status:  Skipped (no source data)")
                print()
        
        # Summary
        print("=" * 60)
        print("SUMMARY:")
        print(f"Total mods in collection: {total_mods}")
        print(f"Mods skipped (start_from): {skipped_count}")
        if not preview_only:
            print(f"Mods processed: {processed_count}")
        print("=" * 60)
    
    except FileNotFoundError:
        print(f"Error: File '{json_file_path}' not found")
    except json.JSONDecodeError as e:
        print(f"Error: Invalid JSON format - {e}")
    except Exception as e:
        print(f"Error: {e}")


def run_requests(mod_id, file_id, user_id, api_key):
    """
    Open NexusMods download link for the specified mod.
    
    Args:
        mod_id (int): NexusMods mod ID
        file_id (int): NexusMods file ID
        user_id (str): NexusMods user ID
        api_key (str): NexusMods API key
    """
    try:
        timestamp = int(time.time()) + (2 * 24 * 60 * 60)  # 2 days from now
        download_url = f"nxm://cyberpunk2077/mods/{mod_id}/files/{file_id}?key={api_key}&expires={timestamp}&user_id={user_id}"
        
        # Open the download link
        os.startfile(download_url)
        print(f"     Download link opened successfully")
        
    except Exception as e:
        print(f"     Error opening download link: {e}")


def get_user_inputs():
    """Get user inputs for configuration."""
    print("=" * 60)
    print("NEXUSMODS MOD COLLECTION DOWNLOADER")
    print("=" * 60)
    
    # Get user ID
    while True:
        user_id = input("Enter your NexusMods User ID: ").strip()
        if user_id:
            break
        print("User ID cannot be empty. Please try again.")
    
    # Get API key
    while True:
        api_key = input("Enter your NexusMods API Key: ").strip()
        if api_key:
            break
        print("API Key cannot be empty. Please try again.")
    
    # Get starting position
    while True:
        try:
            start_input = input("Start from which mod? (Enter number, or press Enter for 1): ").strip()
            if not start_input:
                start_from = 1
                break
            start_from = int(start_input)
            if start_from < 1:
                print("Starting position must be 1 or greater.")
                continue
            break
        except ValueError:
            print("Please enter a valid number.")
    
    # Get delay
    while True:
        try:
            delay_input = input("Delay between downloads in seconds? (Enter number, or press Enter for 10): ").strip()
            if not delay_input:
                delay = 10
                break
            delay = int(delay_input)
            if delay < 0:
                print("Delay must be 0 or greater.")
                continue
            break
        except ValueError:
            print("Please enter a valid number.")
    
    # Preview mode
    preview_input = input("Preview mode only? (y/N): ").strip().lower()
    preview_only = preview_input in ['y', 'yes']
    
    return user_id, api_key, start_from, delay, preview_only


def main():
    """Main function to run the script."""
    # Default file path
    json_file_path = "collection.json"
    
    # Allow command line argument for file path
    if len(sys.argv) > 1:
        json_file_path = sys.argv[1]
    
    # Check if file exists
    if not os.path.exists(json_file_path):
        print(f"Error: File '{json_file_path}' not found")
        print("Please make sure the collection.json file is in the same directory as this script,")
        print("or provide the correct path as a command line argument.")
        return
    
    # Get user inputs
    user_id, api_key, start_from, delay, preview_only = get_user_inputs()
    
    print(f"\nReading mod information from: {json_file_path}")
    print("=" * 60)
    
    # Process the mods
    extract_mod_info(json_file_path, user_id, api_key, start_from, delay, preview_only)


if __name__ == "__main__":
    main()
