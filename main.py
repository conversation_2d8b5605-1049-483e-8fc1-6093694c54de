import json
import sys
import os
import time
from time import sleep


def extract_mod_info(json_file_path):
    """
    Read the JSON file and extract modId and fileId from each mod in the mods array.
    
    Args:
        json_file_path (str): Path to the collection.json file
    """
    try:
        # Read the JSON file
        with open(json_file_path, 'r', encoding='utf-8') as file:
            data = json.load(file)
        
        # Check if 'mods' key exists
        if 'mods' not in data:
            print("Error: 'mods' key not found in JSON file")
            return
        
        mods = data['mods']
        print(f"Found {len(mods)} mods in the collection")
        print("-" * 50)
        
        # Iterate through each mod and extract modId and fileId
        for i, mod in enumerate(mods, 1):
            mod_name = mod.get('name', 'Unknown')
            
            # Check if source exists and has the required fields
            if 'source' in mod:
                source = mod['source']
                mod_id = source.get('modId', 'N/A')
                file_id = source.get('fileId', 'N/A')
                
                print(f"{i:3d}. {mod_name}")
                print(f"     modId:  {mod_id}")
                print(f"     fileId: {file_id}")
                print()
                run_requests(mod_id, file_id)
            else:
                print(f"{i:3d}. {mod_name}")
                print(f"     modId:  N/A (no source)")
                print(f"     fileId: N/A (no source)")
                print()
    
    except FileNotFoundError:
        print(f"Error: File '{json_file_path}' not found")
    except json.JSONDecodeError as e:
        print(f"Error: Invalid JSON format - {e}")
    except Exception as e:
        print(f"Error: {e}")



def main():
    """Main function to run the script."""
    # Default file path
    json_file_path = "collection.json"

    token = input("Enter your NexusMods API token: ")
    
    # Allow command line argument for file path
    if len(sys.argv) > 1:
        json_file_path = sys.argv[1]
    
    print(f"Reading mod information from: {json_file_path}")
    print("=" * 60)
    
    extract_mod_info(json_file_path)

def run_requests(mod_id, file_id):
    timestamp = int(time.time()) + (2 * 24 * 60 * 60) 
    os.(f"nxm://cyberpunk2077/mods/{mod_id}/files/{file_id}?key=FsHsgF-YW4-ISzPUdajOZg&expires={timestamp}&user_id=187998114")
    print(r.json())
    sleep(10)
    print(r.status_code)



if __name__ == "__main__":
    main()
