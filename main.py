import json
import sys
import os
import time
import requests
import re
import gzip
import io
from time import sleep
from bs4 import BeautifulSoup
import brotli

def extract_mod_info(json_file_path, session, start_from=1, delay=10, preview_only=False):
    """
    Read the JSON file and extract modId and fileId from each mod in the mods array.

    Args:
        json_file_path (str): Path to the collection.json file
        session (requests.Session): Authenticated requests session with cookies
        start_from (int): Mod number to start from (1-based)
        delay (int): Delay in seconds between mod downloads
        preview_only (bool): If True, only show mod info without downloading
    """
    try:
        # Read the JSON file
        with open(json_file_path, 'r', encoding='utf-8') as file:
            data = json.load(file)
        
        # Check if 'mods' key exists
        if 'mods' not in data:
            print("Error: 'mods' key not found in JSON file")
            return
        
        mods = data['mods']
        total_mods = len(mods)
        
        # Validate start_from parameter
        if start_from < 1 or start_from > total_mods:
            print(f"Error: start_from must be between 1 and {total_mods}")
            return
        
        print(f"Found {total_mods} mods in the collection")
        if start_from > 1:
            print(f"Starting from mod #{start_from} (skipping {start_from - 1} mods)")
        print(f"Mode: {'Preview only' if preview_only else 'Download'}")
        print("-" * 60)
        
        # Show collection info if available
        if 'info' in data:
            info = data['info']
            collection_name = info.get('name', 'Unknown Collection')
            author = info.get('author', 'Unknown Author')
            print(f"Collection: {collection_name} by {author}")
            print("-" * 60)
        
        processed_count = 0
        skipped_count = start_from - 1
        
        # Iterate through each mod starting from the specified position
        for i, mod in enumerate(mods, 1):
            if i < start_from:
                continue
                
            mod_name = mod.get('name', 'Unknown')
            mod_version = mod.get('version', 'Unknown')
            mod_author = mod.get('author', 'Unknown')
            
            # Check if source exists and has the required fields
            if 'source' in mod:
                source = mod['source']
                mod_id = source.get('modId', 'N/A')
                file_id = source.get('fileId', 'N/A')
                
                print(f"{i:3d}. {mod_name} v{mod_version}")
                print(f"     Author:  {mod_author}")
                print(f"     modId:   {mod_id}")
                print(f"     fileId:  {file_id}")
                
                if not preview_only and mod_id != 'N/A' and file_id != 'N/A':
                    print(f"     Status:  Fetching download page...")
                    download_url = fetch_download_url(session, mod_id, file_id)
                    if download_url:
                        print(f"     Download URL: {download_url}")
                        print(f"     Status:  Opening download link...")
                        try:
                            os.startfile(download_url)
                            print(f"     ✓ Download link opened successfully")
                        except Exception as e:
                            print(f"     ✗ Error opening download link: {e}")
                            # Save HTML for debugging
                    else:
                        print(f"     ✗ Could not extract download URL")
                    processed_count += 1

                    # Show progress
                    remaining = total_mods - i
                    print(f"     Progress: {processed_count} processed, {remaining} remaining")

                    if remaining > 0 and delay > 0:
                        print(f"     Waiting {delay} seconds before next mod...")
                        sleep(delay)
                else:
                    print(f"     Status:  {'Preview mode - not downloading' if preview_only else 'Skipped (missing mod/file ID)'}")
                
                print()
            else:
                print(f"{i:3d}. {mod_name} v{mod_version}")
                print(f"     Author:  {mod_author}")
                print(f"     modId:   N/A (no source)")
                print(f"     fileId:  N/A (no source)")
                print(f"     Status:  Skipped (no source data)")
                print()
        
        # Summary
        print("=" * 60)
        print("SUMMARY:")
        print(f"Total mods in collection: {total_mods}")
        print(f"Mods skipped (start_from): {skipped_count}")
        if not preview_only:
            print(f"Mods processed: {processed_count}")
        print("=" * 60)
    
    except FileNotFoundError:
        print(f"Error: File '{json_file_path}' not found")
    except json.JSONDecodeError as e:
        print(f"Error: Invalid JSON format - {e}")
    except Exception as e:
        print(f"Error: {e}")


def fetch_download_url(session, mod_id, file_id):
    """
    Fetch the download URL from NexusMods using authenticated session.

    Args:
        session (requests.Session): Authenticated requests session
        mod_id (int): NexusMods mod ID
        file_id (int): NexusMods file ID

    Returns:
        str: NXM download URL if found, None otherwise
    """
    try:
        # Construct the download page URL
        download_url = f"https://www.nexusmods.com/cyberpunk2077/mods/{mod_id}?tab=files&file_id={file_id}&nmm=1&mtm_source=nexusmodsapp&mtm_campaign=collections"

        # Set headers to mimic a real browser
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate, br',
            'DNT': '1',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache',
        }

        # Make the request
        response = session.get(download_url, headers=headers, stream=True)
        response.raise_for_status()
        print(f"     Response Headers:")
        for key, value in response.headers.items():
            print(f"     - {key}: {value}")

        print(f"     Successfully fetched page (status: {response.status_code})")
        print(f"     Content-Type: {response.headers.get('content-type', 'unknown')}")
        print(f"     Content-Encoding: {response.headers.get('content-encoding', 'none')}")

        # Get the text content with proper encoding handling
        try:
            compressed = response.raw.read()
            decompressed = brotli.decompress(compressed)
            html_content = decompressed.decode('utf-8')

            print(f"     HTML content length: {len(html_content)} characters")

            # Verify content looks like HTML
            if '<html' in html_content.lower() or '<!doctype' in html_content.lower():
                print(f"     ✓ Content appears to be valid HTML")
            else:
                print(f"     ⚠ Content may not be valid HTML (first 100 chars): {html_content[:100]}")

        except Exception as e:
            print(f"     Error getting text content: {e}")
            # Final fallback to raw content
            try:
                html_content = response.content.decode('utf-8', errors='replace')
                print(f"     Using fallback decoding, length: {len(html_content)} characters")
            except Exception as e2:
                print(f"     Complete failure to decode content: {e2}")
                return None

        # Parse the HTML to extract the download URL from slowDownloadButton
        download_url_result = parse_download_url_from_html(html_content, mod_id, file_id)
        return download_url_result

    except requests.RequestException as e:
        print(f"     Error fetching download page: {e}")
        return None
    except Exception as e:
        print(f"     Unexpected error: {e}")
        return None


def parse_download_url_from_html(html_content, mod_id, file_id):
    """
    Parse the download URL from the HTML content by looking for the slowDownloadButton.

    Args:
        html_content (str): HTML content of the download page
        mod_id (int): Mod ID for reference
        file_id (int): File ID for reference

    Returns:
        str: NXM download URL if found, None otherwise
    """
    try:
        # Check if we have valid HTML content
        if not html_content or len(html_content.strip()) < 100:
            print(f"     ✗ HTML content is too short or empty (length: {len(html_content) if html_content else 0})")
            return None

        soup = BeautifulSoup(html_content, 'html.parser')

        # Look for the slowDownloadButton with data-download-url attribute
        slow_download_button = soup.find('button', id='slowDownloadButton')
        if slow_download_button:
            print(f"     ✓ Found slowDownloadButton element")
            if slow_download_button.get('data-download-url'):
                download_url = slow_download_button['data-download-url']
                print(f"     ✓ Found download URL in slowDownloadButton")
                return download_url
            else:
                print(f"     ✗ slowDownloadButton found but no data-download-url attribute")
                print(f"     Button attributes: {slow_download_button.attrs}")
        else:
            print(f"     ✗ slowDownloadButton not found")

        # Alternative: Look for any element with data-download-url attribute
        download_elements = soup.find_all(attrs={'data-download-url': True})
        print(f"     Found {len(download_elements)} elements with data-download-url attribute")
        for element in download_elements:
            download_url = element['data-download-url']
            print(f"     ✓ Found download URL in element: {element.name} (id: {element.get('id', 'none')})")
            return download_url

        # Alternative: Look for NXM links in any attributes or text
        # Search for nxm:// URLs in the entire HTML
        nxm_pattern = r'nxm://[^"\'\s<>]+'
        nxm_matches = re.findall(nxm_pattern, html_content)
        print(f"     Found {len(nxm_matches)} NXM URLs in HTML content")

        if nxm_matches:
            # Filter for cyberpunk2077 and matching mod/file IDs if possible
            for nxm_url in nxm_matches:
                if 'cyberpunk2077' in nxm_url and str(mod_id) in nxm_url and str(file_id) in nxm_url:
                    print(f"     ✓ Found matching NXM URL in HTML content: {nxm_url[:100]}...")
                    return nxm_url
            # If no exact match, return the first cyberpunk2077 NXM URL
            for nxm_url in nxm_matches:
                if 'cyberpunk2077' in nxm_url:
                    print(f"     ✓ Found Cyberpunk 2077 NXM URL in HTML content: {nxm_url[:100]}...")
                    return nxm_url
            # If no cyberpunk2077 URLs, show what we found
            print(f"     Found NXM URLs but none for cyberpunk2077:")
            for i, url in enumerate(nxm_matches[:3]):  # Show first 3
                print(f"       {i+1}. {url[:100]}...")

        # Look for download buttons or links that might contain the URL
        download_buttons = soup.find_all(['button', 'a'], class_=re.compile(r'download', re.I))
        print(f"     Found {len(download_buttons)} potential download buttons/links")
        for button in download_buttons[:5]:  # Check first 5
            for attr in ['data-download-url', 'href', 'data-url', 'data-link']:
                if button.get(attr) and 'nxm://' in str(button.get(attr)):
                    print(f"     ✓ Found NXM URL in {button.name} {attr}: {button[attr][:100]}...")
                    return button[attr]

        print(f"     ✗ No download URL found for mod {mod_id}, file {file_id}")

        # For debugging, save the HTML and analysis to inspect manually
        save_debug_files(html_content, mod_id, file_id, soup)

        return None

    except Exception as e:
        print(f"     ✗ Error parsing HTML: {e}")
        return None


def save_debug_files(html_content, mod_id, file_id, soup=None):
    """Save debug files for manual inspection."""
    base_filename = f'debug_mod_{mod_id}_file_{file_id}'

    # Save the raw HTML
    html_filename = f'{base_filename}.html'
    try:
        # Ensure we can write the content as readable text
        if isinstance(html_content, bytes):
            try:
                html_text = html_content.decode('utf-8')
            except UnicodeDecodeError:
                html_text = html_content.decode('utf-8', errors='replace')
        else:
            html_text = html_content

        # Double-check if content is still binary-looking
        if len(html_text) > 0 and ord(html_text[0]) < 32 and html_text[0] not in '\t\n\r':
            print(f"     Debug: Content still appears binary, attempting decompression...")
            import gzip
            import io
            try:
                # If html_content is a string but looks binary, it might be incorrectly decoded
                if isinstance(html_content, str):
                    # Convert back to bytes and try decompression
                    binary_content = html_content.encode('latin1')  # Preserve byte values
                else:
                    binary_content = html_content

                html_text = gzip.decompress(binary_content).decode('utf-8')
                print(f"     Debug: Successfully decompressed content for saving")
            except Exception as decomp_error:
                print(f"     Debug: Decompression failed: {decomp_error}")
                # Use the original text with replacements for unreadable chars
                html_text = ''.join(c if ord(c) >= 32 or c in '\t\n\r' else f'[BYTE:{ord(c)}]' for c in html_text[:10000])
                html_text += "\n\n<!-- Content was binary/compressed and may not be fully readable -->"

        with open(html_filename, 'w', encoding='utf-8') as f:
            f.write(html_text)
        print(f"     Debug: HTML saved to {html_filename}")

        # Verify the saved file is readable
        with open(html_filename, 'r', encoding='utf-8') as f:
            test_content = f.read(100)
            if '<html' in test_content.lower() or '<!doctype' in test_content.lower():
                print(f"     Debug: ✓ Saved HTML file appears readable")
            else:
                print(f"     Debug: ⚠ Saved HTML file may still have issues")

    except Exception as e:
        print(f"     Debug: Could not save HTML file: {e}")
        # Try saving as binary with a note
        try:
            with open(f'{base_filename}.bin', 'wb') as f:
                if isinstance(html_content, str):
                    f.write(html_content.encode('utf-8', errors='replace'))
                else:
                    f.write(html_content)

            # Also create a note file
            with open(f'{base_filename}_note.txt', 'w', encoding='utf-8') as f:
                f.write("The HTML content was saved as binary because it could not be properly decoded.\n")
                f.write("This usually means the content is compressed or in an unexpected format.\n")
                f.write(f"File size: {len(html_content)} bytes\n")
                f.write(f"Content type: {type(html_content)}\n")

            print(f"     Debug: Binary content saved to {base_filename}.bin with note")
        except Exception as e2:
            print(f"     Debug: Could not save binary file either: {e2}")

    # Save analysis file
    analysis_filename = f'{base_filename}_analysis.txt'
    try:
        with open(analysis_filename, 'w', encoding='utf-8') as f:
            f.write(f"Debug Analysis for Mod {mod_id}, File {file_id}\n")
            f.write("=" * 50 + "\n\n")

            f.write(f"HTML Content Length: {len(html_content) if html_content else 0}\n")
            f.write(f"Content Type: {type(html_content)}\n\n")

            if soup:
                # Find all buttons
                buttons = soup.find_all('button')
                f.write(f"Total buttons found: {len(buttons)}\n")
                for i, button in enumerate(buttons[:10]):  # First 10 buttons
                    f.write(f"  Button {i+1}: id='{button.get('id', 'none')}', class='{button.get('class', 'none')}'\n")
                    if button.get('data-download-url'):
                        f.write(f"    data-download-url: {button['data-download-url']}\n")
                f.write("\n")

                # Find all elements with data-download-url
                download_elements = soup.find_all(attrs={'data-download-url': True})
                f.write(f"Elements with data-download-url: {len(download_elements)}\n")
                for elem in download_elements:
                    f.write(f"  {elem.name}: {elem.get('data-download-url')}\n")
                f.write("\n")

                # Search for NXM URLs
                nxm_pattern = r'nxm://[^"\'\s<>]+'
                nxm_matches = re.findall(nxm_pattern, str(soup))
                f.write(f"NXM URLs found: {len(nxm_matches)}\n")
                for url in nxm_matches:
                    f.write(f"  {url}\n")

        print(f"     Debug: Analysis saved to {analysis_filename}")
    except Exception as e:
        print(f"     Debug: Could not save analysis file: {e}")


def get_cookies_from_user():
    """
    Get cookies from user input and create an authenticated session.

    Returns:
        requests.Session: Authenticated session with cookies, or None if failed
    """
    cookies_file = "nexusmods_cookies.json"

    # Try to load existing cookies first
    session = load_cookies_from_file(cookies_file)
    if session and test_authentication(session):
        print("✓ Using saved authentication cookies")
        return session

    print("=" * 60)
    print("NEXUSMODS COOKIE AUTHENTICATION")
    print("=" * 60)
    print("To avoid bot detection, please provide your NexusMods cookies manually.")
    print()
    print("How to get your cookies:")
    print("1. Open your browser and go to https://www.nexusmods.com")
    print("2. Log in to your account")
    print("3. Open Developer Tools (F12)")
    print("4. Go to Application/Storage tab > Cookies > https://www.nexusmods.com")
    print("5. Find and copy the following cookie values:")
    print()

    # Get essential cookies from user
    cookies = {}

    # Get session cookie (most important)
    while True:
        session_cookie = input("Enter your 'nexusmods_session' cookie value: ").strip()
        if session_cookie:
            cookies['nexusmods_session'] = session_cookie
            break
        print("Session cookie is required. Please try again.")

    # Get CSRF token (often needed)
    csrf_token = input("Enter your 'csrf_token' cookie value (or press Enter to skip): ").strip()
    if csrf_token:
        cookies['csrf_token'] = csrf_token

    # Get any additional cookies
    print("\nOptional: Enter any additional cookies (format: name=value, press Enter when done):")
    while True:
        additional_cookie = input("Additional cookie (or Enter to finish): ").strip()
        if not additional_cookie:
            break
        if '=' in additional_cookie:
            name, value = additional_cookie.split('=', 1)
            cookies[name.strip()] = value.strip()
            print(f"Added cookie: {name.strip()}")
        else:
            print("Invalid format. Use: name=value")

    # Create session with cookies
    session = create_session_from_cookies_dict(cookies)

    # Test authentication
    if test_authentication(session):
        print("✓ Authentication successful!")
        save_cookies_to_file(cookies, cookies_file)
        print("✓ Cookies saved for future use")
        return session
    else:
        print("✗ Authentication failed. Please check your cookies and try again.")
        return None


def save_cookies_to_file(cookies_dict, filename):
    """Save cookies dictionary to a JSON file."""
    try:
        with open(filename, 'w') as f:
            json.dump(cookies_dict, f, indent=2)
    except Exception as e:
        print(f"Warning: Could not save cookies: {e}")


def load_cookies_from_file(filename):
    """Load cookies from JSON file and create a session."""
    try:
        with open(filename, 'r') as f:
            cookies_dict = json.load(f)
        return create_session_from_cookies_dict(cookies_dict)
    except (FileNotFoundError, json.JSONDecodeError):
        return None


def create_session_from_cookies_dict(cookies_dict):
    """Create a requests session with the given cookies dictionary."""
    session = requests.Session()
    for name, value in cookies_dict.items():
        session.cookies.set(name, value, domain='.nexusmods.com')
    return session


def test_authentication(session):
    """Test if the session is properly authenticated."""
    try:
        # Try to access a page that requires authentication
        response = session.get("https://www.nexusmods.com/users/myaccount", timeout=10)
        return response.status_code == 200 and "myaccount" in response.url
    except:
        return False


def get_user_inputs():
    """Get user inputs for configuration."""
    print("=" * 60)
    print("NEXUSMODS MOD COLLECTION DOWNLOADER")
    print("=" * 60)

    # Get starting position
    while True:
        try:
            start_input = input("Start from which mod? (Enter number, or press Enter for 1): ").strip()
            if not start_input:
                start_from = 1
                break
            start_from = int(start_input)
            if start_from < 1:
                print("Starting position must be 1 or greater.")
                continue
            break
        except ValueError:
            print("Please enter a valid number.")

    # Get delay
    while True:
        try:
            delay_input = input("Delay between downloads in seconds? (Enter number, or press Enter for 10): ").strip()
            if not delay_input:
                delay = 10
                break
            delay = int(delay_input)
            if delay < 0:
                print("Delay must be 0 or greater.")
                continue
            break
        except ValueError:
            print("Please enter a valid number.")

    # Preview mode
    preview_input = input("Preview mode only? (y/N): ").strip().lower()
    preview_only = preview_input in ['y', 'yes']

    return start_from, delay, preview_only


def main():
    """Main function to run the script."""
    # Default file path
    json_file_path = "collection.json"

    # Allow command line argument for file path
    if len(sys.argv) > 1:
        json_file_path = sys.argv[1]

    # Check if file exists
    if not os.path.exists(json_file_path):
        print(f"Error: File '{json_file_path}' not found")
        print("Please make sure the collection.json file is in the same directory as this script,")
        print("or provide the correct path as a command line argument.")
        return

    # Authenticate with NexusMods using cookies
    session = get_cookies_from_user()
    if not session:
        print("✗ Authentication failed. Cannot proceed.")
        return

    # Get user inputs
    start_from, delay, preview_only = get_user_inputs()

    print(f"\nReading mod information from: {json_file_path}")
    print("=" * 60)

    # Process the mods
    extract_mod_info(json_file_path, session, start_from, delay, preview_only)


if __name__ == "__main__":
    main()
