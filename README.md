# NexusMods Collection Downloader

Enhanced Python script to download Cyberpunk 2077 mod collections from NexusMods using manual cookie authentication and requests for download URL extraction.

## Features

- **Manual Cookie Authentication**: User provides cookies to avoid all bot detection
- **Cookie Management**: Saves and reuses authentication cookies
- **No Browser Automation**: Completely avoids bot detection by not using automated browsers
- **Download URL Extraction**: Parses HTML to extract NXM download URLs from mod pages
- **Skip Mods**: Start downloading from a specific mod number (e.g., start from mod #4)
- **Configurable Delay**: Set custom delay between downloads to avoid rate limiting
- **Preview Mode**: View mod information without actually downloading
- **Progress Tracking**: Shows download progress and remaining mods
- **Enhanced Display**: Shows mod version, author, and collection information
- **Error Handling**: Better error messages and validation
- **Summary Report**: Shows statistics at the end
- **Enhanced Debug Support**: Saves readable HTML files and detailed analysis when parsing fails

## Requirements

- Python 3.7+
- NexusMods account
- Dependencies listed in requirements.txt

**Note**: Uses manual cookie input to completely avoid bot detection

## Setup

1. **Install Dependencies**:

   ```bash
   pip install -r requirements.txt
   ```

2. **Get Your Cookies**: You'll need to extract cookies from your browser (instructions below)

## Getting Your Cookies

Since NexusMods has strong anti-bot protection, this script uses manual cookie authentication:

1. **Open your browser** and go to https://www.nexusmods.com
2. **Log in** to your NexusMods account
3. **Open Developer Tools** (Press F12)
4. **Go to Application/Storage tab** → Cookies → https://www.nexusmods.com
5. **Find these cookies**:

   - `nexusmods_session` (required)
   - `csrf_token` (recommended)
   - Any other cookies you see

6. **Copy the cookie values** (you'll paste them when the script asks)

## Usage

### Basic Usage

```bash
python main.py
```

### With Custom Collection File

```bash
python main.py path/to/your/collection.json
```

### Authentication Process

1. **First Run**: The script will ask you to provide your NexusMods cookies
2. **Manual Cookie Input**: Copy and paste cookies from your browser
3. **Cookie Saving**: The script saves your cookies for future use
4. **Subsequent Runs**: Uses saved cookies (no need to re-enter them)

### Interactive Prompts

When you run the script, it will ask for:

1. **Start from which mod?**: Enter a number to skip mods (default: 1)
2. **Delay between downloads**: Seconds to wait between each download (default: 10)
3. **Preview mode only?**: Enter 'y' to only view mod info without downloading

### Example Session

```text
============================================================
NEXUSMODS COOKIE AUTHENTICATION
============================================================
To avoid bot detection, please provide your NexusMods cookies manually.

How to get your cookies:
1. Open your browser and go to https://www.nexusmods.com
2. Log in to your account
3. Open Developer Tools (F12)
4. Go to Application/Storage tab > Cookies > https://www.nexusmods.com
5. Find and copy the following cookie values:

Enter your 'nexusmods_session' cookie value: [your-session-cookie]
Enter your 'csrf_token' cookie value (or press Enter to skip): [your-csrf-token]
✓ Authentication successful!
✓ Cookies saved for future use

============================================================
NEXUSMODS MOD COLLECTION DOWNLOADER
============================================================
Start from which mod? (Enter number, or press Enter for 1): 4
Delay between downloads in seconds? (Enter number, or press Enter for 10): 5
Preview mode only? (y/N): n

Reading mod information from: collection.json
============================================================
Found 150 mods in the collection
Starting from mod #4 (skipping 3 mods)
Mode: Download
------------------------------------------------------------
Collection: Welcome to Night City 2.21 by z9r
------------------------------------------------------------

  4. Native Settings UI 1.96 v1.96
     Author:  keanuWheeze
     modId:   3518
     fileId:  63684
     Status:  Fetching download page...
     Successfully fetched page (status: 200)
     ✓ Found download URL in slowDownloadButton
     Download URL: nxm://cyberpunk2077/mods/3518/files/63684?key=abc123&expires=1234567890&user_id=12345678
     Status:  Opening download link...
     ✓ Download link opened successfully
     Progress: 1 processed, 146 remaining
     Waiting 5 seconds before next mod...
```

## Features Explained

### Manual Cookie Authentication

- **Zero Bot Detection**: No browser automation means no bot detection
- **User-provided cookies**: You manually copy cookies from your browser
- **Saves cookies for reuse**: Only need to provide cookies once
- **100% Reliable**: Works even with the strongest anti-bot measures

### Download URL Parsing

- **✅ IMPLEMENTED**: HTML parsing extracts NXM download URLs from the `slowDownloadButton`
- Looks for `data-download-url` attribute on elements with ID `slowDownloadButton`
- Falls back to searching for any NXM URLs in the HTML content
- Automatically opens download links in your mod manager

### Parsing Implementation

The script now automatically:

1. **Finds the slowDownloadButton**: Looks for `<button id="slowDownloadButton" data-download-url="nxm://...">`
2. **Extracts the NXM URL**: Gets the complete download URL from the `data-download-url` attribute
3. **Opens the download**: Automatically launches the NXM link in your mod manager
4. **Fallback methods**: If the button isn't found, searches for any NXM URLs in the HTML
5. **Enhanced debug support**: Saves readable HTML files and detailed analysis when URLs aren't found

### Debug Files

When the script can't find download URLs, it automatically creates debug files:

- **`debug_mod_[ID]_file_[ID].html`**: The complete HTML page (readable text format)
- **`debug_mod_[ID]_file_[ID]_analysis.txt`**: Detailed analysis including:
  - All buttons found on the page
  - Elements with download-related attributes
  - All NXM URLs found in the HTML
  - Content statistics and parsing details

These files help you manually inspect what the script is seeing and troubleshoot parsing issues.

### Skip Mods Feature

- Enter a number to start from that mod position
- Useful if you've already downloaded some mods and want to continue
- Example: Enter "10" to start from the 10th mod, skipping the first 9

### Preview Mode

- Shows all mod information without fetching download pages
- Useful for reviewing what mods are in the collection
- No delays applied in preview mode

### Error Handling

- Validates input parameters
- Handles missing files gracefully
- Shows clear error messages for common issues

### Why Manual Cookies?

The script uses manual cookie input instead of browser automation because:

- **Zero Bot Detection**: No automated browser means no bot detection at all
- **No Captchas**: Since you're using real browser cookies, no captchas are triggered
- **100% Success Rate**: Works even with the strongest anti-automation measures
- **Simple and Reliable**: No complex browser automation that can break
- **Lightweight**: No need for ChromeDriver or browser dependencies

## Detailed Cookie Instructions

### For Chrome/Edge

1. Go to <https://www.nexusmods.com> and log in
2. Press `F12` to open Developer Tools
3. Click the **Application** tab (or **Storage** in Firefox)
4. In the left sidebar, expand **Cookies** and click **<https://www.nexusmods.com>**
5. Find the `nexusmods_session` cookie and copy its **Value**
6. Optionally, also copy the `csrf_token` cookie value

### For Firefox

1. Go to <https://www.nexusmods.com> and log in
2. Press `F12` to open Developer Tools
3. Click the **Storage** tab
4. In the left sidebar, expand **Cookies** and click **<https://www.nexusmods.com>**
5. Find the `nexusmods_session` cookie and copy its **Value**

### Cookie Security Note

- **Keep your cookies private** - they give access to your account
- **Don't share them** with anyone
- **The script saves them locally** in `nexusmods_cookies.json`

## Troubleshooting

### Common Issues

1. **"File not found" error**: Make sure `collection.json` is in the same directory as the script
2. **Download links not opening**: Ensure you have a mod manager installed that handles NXM links
3. **Authentication failed**: Double-check your cookie values and make sure you're logged in to NexusMods
4. **Cookies expired**: If authentication fails, get fresh cookies from your browser

### Getting Help

- Check that your collection.json file is valid JSON
- Ensure your NexusMods API key has the necessary permissions
- Make sure your mod manager is set up to handle NXM protocol links

## Notes

- The script uses a 2-day expiration time for download links
- Downloads are opened in your default mod manager via NXM protocol
- The script respects rate limiting with configurable delays
- All mod information is displayed before downloading for transparency
