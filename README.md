# NexusMods Collection Downloader

Enhanced Python script to download Cyberpunk 2077 mod collections from NexusMods using Selenium authentication and requests for download key extraction.

## Features

- **Undetected Chrome Authentication**: Uses `undetected-chromedriver` for superior bot detection avoidance
- **Cookie Management**: Saves and reuses authentication cookies
- **Advanced Anti-Detection**: Bypasses browser fingerprinting and automation detection
- **Download URL Extraction**: Parses HTML to extract NXM download URLs from mod pages
- **Skip Mods**: Start downloading from a specific mod number (e.g., start from mod #4)
- **Configurable Delay**: Set custom delay between downloads to avoid rate limiting
- **Preview Mode**: View mod information without actually downloading
- **Progress Tracking**: Shows download progress and remaining mods
- **Enhanced Display**: Shows mod version, author, and collection information
- **Error Handling**: Better error messages and validation
- **Summary Report**: Shows statistics at the end
- **Debug Support**: Saves HTML files when parsing fails for manual inspection

## Requirements

- Python 3.7+
- Chrome browser installed
- NexusMods account
- Dependencies listed in requirements.txt

**Note**: Uses `undetected-chromedriver` for superior bot detection avoidance

## Setup

1. **Install Dependencies**:

   ```bash
   pip install -r requirements.txt
   ```

2. **Chrome Browser**: Make sure you have Chrome installed (undetected-chromedriver auto-manages ChromeDriver)

## Usage

### Basic Usage

```bash
python main.py
```

### With Custom Collection File

```bash
python main.py path/to/your/collection.json
```

### Authentication Process

1. **First Run**: The script will open Chrome browser for NexusMods login
2. **Manual Login**: Log in to your NexusMods account in the browser
3. **Cookie Saving**: The script automatically saves your authentication cookies
4. **Subsequent Runs**: Uses saved cookies (no need to log in again)

### Interactive Prompts

When you run the script, it will ask for:

1. **Start from which mod?**: Enter a number to skip mods (default: 1)
2. **Delay between downloads**: Seconds to wait between each download (default: 10)
3. **Preview mode only?**: Enter 'y' to only view mod info without downloading

### Example Session

```text
============================================================
NEXUSMODS AUTHENTICATION
============================================================
Opening undetected Chrome browser for NexusMods login...
Please log in manually in the browser window that opens.
The script will automatically detect when you're logged in.
Waiting for you to log in...
✓ Login detected!
✓ Cookies saved successfully

============================================================
NEXUSMODS MOD COLLECTION DOWNLOADER
============================================================
Start from which mod? (Enter number, or press Enter for 1): 4
Delay between downloads in seconds? (Enter number, or press Enter for 10): 5
Preview mode only? (y/N): n

Reading mod information from: collection.json
============================================================
Found 150 mods in the collection
Starting from mod #4 (skipping 3 mods)
Mode: Download
------------------------------------------------------------
Collection: Welcome to Night City 2.21 by z9r
------------------------------------------------------------

  4. Native Settings UI 1.96 v1.96
     Author:  keanuWheeze
     modId:   3518
     fileId:  63684
     Status:  Fetching download page...
     Successfully fetched page (status: 200)
     ✓ Found download URL in slowDownloadButton
     Download URL: nxm://cyberpunk2077/mods/3518/files/63684?key=abc123&expires=1234567890&user_id=12345678
     Status:  Opening download link...
     ✓ Download link opened successfully
     Progress: 1 processed, 146 remaining
     Waiting 5 seconds before next mod...
```

## Features Explained

### Undetected Chrome Authentication

- Uses `undetected-chromedriver` for superior bot detection avoidance
- Opens undetected Chrome browser for manual login
- Saves authentication cookies for future use
- Automatically handles anti-bot measures and browser fingerprinting

### Download URL Parsing

- **✅ IMPLEMENTED**: HTML parsing extracts NXM download URLs from the `slowDownloadButton`
- Looks for `data-download-url` attribute on elements with ID `slowDownloadButton`
- Falls back to searching for any NXM URLs in the HTML content
- Automatically opens download links in your mod manager

### Parsing Implementation

The script now automatically:

1. **Finds the slowDownloadButton**: Looks for `<button id="slowDownloadButton" data-download-url="nxm://...">`
2. **Extracts the NXM URL**: Gets the complete download URL from the `data-download-url` attribute
3. **Opens the download**: Automatically launches the NXM link in your mod manager
4. **Fallback methods**: If the button isn't found, searches for any NXM URLs in the HTML
5. **Debug support**: Saves HTML files for manual inspection when URLs aren't found

### Skip Mods Feature

- Enter a number to start from that mod position
- Useful if you've already downloaded some mods and want to continue
- Example: Enter "10" to start from the 10th mod, skipping the first 9

### Preview Mode

- Shows all mod information without fetching download pages
- Useful for reviewing what mods are in the collection
- No delays applied in preview mode

### Error Handling

- Validates input parameters
- Handles missing files gracefully
- Shows clear error messages for common issues

### Why Undetected-ChromeDriver?

The script uses `undetected-chromedriver` instead of regular Selenium because:

- **Superior Bot Detection Avoidance**: Bypasses most anti-automation measures
- **No Manual ChromeDriver Management**: Automatically downloads and manages ChromeDriver
- **Realistic Browser Behavior**: Mimics human browsing patterns
- **Reduced Captchas**: Less likely to trigger security challenges
- **Better Success Rate**: Higher reliability for accessing protected content

## Troubleshooting

### Common Issues

1. **"File not found" error**: Make sure `collection.json` is in the same directory as the script
2. **Download links not opening**: Ensure you have a mod manager installed that handles NXM links
3. **API errors**: Verify your User ID and API Key are correct

### Getting Help

- Check that your collection.json file is valid JSON
- Ensure your NexusMods API key has the necessary permissions
- Make sure your mod manager is set up to handle NXM protocol links

## Notes

- The script uses a 2-day expiration time for download links
- Downloads are opened in your default mod manager via NXM protocol
- The script respects rate limiting with configurable delays
- All mod information is displayed before downloading for transparency
