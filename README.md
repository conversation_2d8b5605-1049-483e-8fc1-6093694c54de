# NexusMods Collection Downloader

Enhanced Python script to download Cyberpunk 2077 mod collections from NexusMods.

## Features

- **User Input for Credentials**: Prompts for NexusMods User ID and API Key
- **Skip Mods**: Start downloading from a specific mod number (e.g., start from mod #4)
- **Configurable Delay**: Set custom delay between downloads to avoid rate limiting
- **Preview Mode**: View mod information without actually downloading
- **Progress Tracking**: Shows download progress and remaining mods
- **Enhanced Display**: Shows mod version, author, and collection information
- **Error Handling**: Better error messages and validation
- **Summary Report**: Shows statistics at the end

## Requirements

- Python 3.6+
- Windows OS (uses `os.startfile()` for NXM links)
- NexusMods account with API access
- Mod manager that supports NXM links (like Vortex)

## Setup

1. **Get your NexusMods API Key**:
   - Go to [NexusMods API Key page](https://www.nexusmods.com/users/myaccount?tab=api)
   - Generate a new API key
   - Copy the key for use with this script

2. **Get your User ID**:
   - Go to your NexusMods profile
   - Your User ID is the number in the URL (e.g., `https://www.nexusmods.com/users/********`)

## Usage

### Basic Usage
```bash
python main.py
```

### With Custom Collection File
```bash
python main.py path/to/your/collection.json
```

### Interactive Prompts

When you run the script, it will ask for:

1. **NexusMods User ID**: Your numeric user ID
2. **NexusMods API Key**: Your API key from NexusMods
3. **Start from which mod?**: Enter a number to skip mods (default: 1)
4. **Delay between downloads**: Seconds to wait between each download (default: 10)
5. **Preview mode only?**: Enter 'y' to only view mod info without downloading

### Example Session

```
============================================================
NEXUSMODS MOD COLLECTION DOWNLOADER
============================================================
Enter your NexusMods User ID: ********
Enter your NexusMods API Key: your-api-key-here
Start from which mod? (Enter number, or press Enter for 1): 4
Delay between downloads in seconds? (Enter number, or press Enter for 10): 5
Preview mode only? (y/N): n

Reading mod information from: collection.json
============================================================
Found 150 mods in the collection
Starting from mod #4 (skipping 3 mods)
Mode: Download
------------------------------------------------------------
Collection: Welcome to Night City 2.21 by z9r
------------------------------------------------------------

  4. Native Settings UI 1.96 v1.96
     Author:  keanuWheeze
     modId:   3518
     fileId:  63684
     Status:  Opening download link...
     Download link opened successfully
     Progress: 1 processed, 146 remaining
     Waiting 5 seconds before next mod...
```

## Features Explained

### Skip Mods Feature
- Enter a number to start from that mod position
- Useful if you've already downloaded some mods and want to continue
- Example: Enter "10" to start from the 10th mod, skipping the first 9

### Preview Mode
- Shows all mod information without opening download links
- Useful for reviewing what mods are in the collection
- No delays applied in preview mode

### Error Handling
- Validates input parameters
- Handles missing files gracefully
- Shows clear error messages for common issues

## Troubleshooting

### Common Issues

1. **"File not found" error**: Make sure `collection.json` is in the same directory as the script
2. **Download links not opening**: Ensure you have a mod manager installed that handles NXM links
3. **API errors**: Verify your User ID and API Key are correct

### Getting Help

- Check that your collection.json file is valid JSON
- Ensure your NexusMods API key has the necessary permissions
- Make sure your mod manager is set up to handle NXM protocol links

## Notes

- The script uses a 2-day expiration time for download links
- Downloads are opened in your default mod manager via NXM protocol
- The script respects rate limiting with configurable delays
- All mod information is displayed before downloading for transparency
