# NexusMods Collection Downloader

Enhanced Python script to download Cyberpunk 2077 mod collections from NexusMods using Selenium authentication and requests for download key extraction.

## Features

- **Selenium Authentication**: Automated browser login with cookie persistence
- **Cookie Management**: Saves and reuses authentication cookies
- **Bot Detection Avoidance**: Uses real browser headers and session management
- **Download Key Extraction**: Parses HTML to extract download keys from mod pages
- **Skip Mods**: Start downloading from a specific mod number (e.g., start from mod #4)
- **Configurable Delay**: Set custom delay between downloads to avoid rate limiting
- **Preview Mode**: View mod information without actually downloading
- **Progress Tracking**: Shows download progress and remaining mods
- **Enhanced Display**: Shows mod version, author, and collection information
- **Error Handling**: Better error messages and validation
- **Summary Report**: Shows statistics at the end

## Requirements

- Python 3.7+
- Chrome browser installed
- ChromeDriver (automatically managed by Selenium 4.15+)
- NexusMods account
- Dependencies listed in requirements.txt

## Setup

1. **Install Dependencies**:

   ```bash
   pip install -r requirements.txt
   ```

2. **Chrome Browser**: Make sure you have Chrome installed (ChromeDriver is auto-managed)

## Usage

### Basic Usage

```bash
python main.py
```

### With Custom Collection File

```bash
python main.py path/to/your/collection.json
```

### Authentication Process

1. **First Run**: The script will open Chrome browser for NexusMods login
2. **Manual Login**: Log in to your NexusMods account in the browser
3. **Cookie Saving**: The script automatically saves your authentication cookies
4. **Subsequent Runs**: Uses saved cookies (no need to log in again)

### Interactive Prompts

When you run the script, it will ask for:

1. **Start from which mod?**: Enter a number to skip mods (default: 1)
2. **Delay between downloads**: Seconds to wait between each download (default: 10)
3. **Preview mode only?**: Enter 'y' to only view mod info without downloading

### Example Session

```text
============================================================
NEXUSMODS AUTHENTICATION
============================================================
Opening browser for NexusMods login...
Please log in manually in the browser window that opens.
The script will automatically detect when you're logged in.
Waiting for you to log in...
✓ Login detected!
✓ Cookies saved successfully

============================================================
NEXUSMODS MOD COLLECTION DOWNLOADER
============================================================
Start from which mod? (Enter number, or press Enter for 1): 4
Delay between downloads in seconds? (Enter number, or press Enter for 10): 5
Preview mode only? (y/N): n

Reading mod information from: collection.json
============================================================
Found 150 mods in the collection
Starting from mod #4 (skipping 3 mods)
Mode: Download
------------------------------------------------------------
Collection: Welcome to Night City 2.21 by z9r
------------------------------------------------------------

  4. Native Settings UI 1.96 v1.96
     Author:  keanuWheeze
     modId:   3518
     fileId:  63684
     Status:  Fetching download page...
     Successfully fetched page (status: 200)
     TODO: Parse download key from HTML for mod 3518, file 63684
     HTML length: 45231 characters
     Progress: 1 processed, 146 remaining
     Waiting 5 seconds before next mod...
```

## Features Explained

### Selenium Authentication

- Opens Chrome browser for manual login
- Saves authentication cookies for future use
- Avoids bot detection by using real browser session

### Download Key Parsing

- **IMPORTANT**: You need to implement the HTML parsing logic in the `parse_download_key_from_html()` function
- The function is located in `main.py` around line 180
- Look for the comment: `*** THIS IS WHERE YOU NEED TO IMPLEMENT THE PARSING LOGIC ***`

### Where to Implement Parsing

In the `parse_download_key_from_html()` function, you should:

1. **Use BeautifulSoup** to parse the HTML content
2. **Look for download keys** in these common locations:
   - JavaScript variables: `var downloadKey = "abc123";`
   - Data attributes: `data-download-key="abc123"`
   - Hidden form inputs: `<input type="hidden" name="key" value="abc123">`
   - JSON in script tags: `{"downloadKey": "abc123"}`
3. **Extract and return** the download key string

Example implementation starter:

```python
from bs4 import BeautifulSoup
import re

def parse_download_key_from_html(html_content, mod_id, file_id):
    soup = BeautifulSoup(html_content, 'html.parser')

    # Method 1: Look for JavaScript variables
    scripts = soup.find_all('script')
    for script in scripts:
        if script.string:
            match = re.search(r'downloadKey\s*=\s*["\']([^"\']+)["\']', script.string)
            if match:
                return match.group(1)

    # Method 2: Look for data attributes
    download_button = soup.find(attrs={'data-download-key': True})
    if download_button:
        return download_button['data-download-key']

    # Add more parsing methods as needed...
    return None
```

### Skip Mods Feature

- Enter a number to start from that mod position
- Useful if you've already downloaded some mods and want to continue
- Example: Enter "10" to start from the 10th mod, skipping the first 9

### Preview Mode

- Shows all mod information without fetching download pages
- Useful for reviewing what mods are in the collection
- No delays applied in preview mode

### Error Handling

- Validates input parameters
- Handles missing files gracefully
- Shows clear error messages for common issues

## Troubleshooting

### Common Issues

1. **"File not found" error**: Make sure `collection.json` is in the same directory as the script
2. **Download links not opening**: Ensure you have a mod manager installed that handles NXM links
3. **API errors**: Verify your User ID and API Key are correct

### Getting Help

- Check that your collection.json file is valid JSON
- Ensure your NexusMods API key has the necessary permissions
- Make sure your mod manager is set up to handle NXM protocol links

## Notes

- The script uses a 2-day expiration time for download links
- Downloads are opened in your default mod manager via NXM protocol
- The script respects rate limiting with configurable delays
- All mod information is displayed before downloading for transparency
