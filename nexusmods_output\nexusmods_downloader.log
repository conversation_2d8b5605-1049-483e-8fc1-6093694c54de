2025-06-10 00:07:44,153 - INFO - Starting NexusMods Collection Downloader - Enhanced Edition
2025-06-10 00:07:44,154 - ERROR - Unexpected error in main: name 'logger' is not defined
Traceback (most recent call last):
  File "E:\VSCODE\nexust\main.py", line 1039, in main
    browser_session = BrowserSession()
                      ^^^^^^^^^^^^^^^^
  File "E:\VSCODE\nexust\main.py", line 95, in __init__
    logger.info(f"Browser session initialized: {self.browser_type} (Session ID: {self.session_id})")
    ^^^^^^
NameError: name 'logger' is not defined
2025-06-10 00:07:52,813 - INFO - Starting NexusMods Collection Downloader - Enhanced Edition
2025-06-10 00:07:52,813 - ERROR - Unexpected error in main: name 'logger' is not defined
Traceback (most recent call last):
  File "E:\VSCODE\nexust\main.py", line 1039, in main
    browser_session = BrowserSession()
                      ^^^^^^^^^^^^^^^^
  File "E:\VSCODE\nexust\main.py", line 95, in __init__
    logger.info(f"Browser session initialized: {self.browser_type} (Session ID: {self.session_id})")
    ^^^^^^
NameError: name 'logger' is not defined
2025-06-10 00:09:04,599 - INFO - Starting NexusMods Collection Downloader - Enhanced Edition
2025-06-10 00:09:04,599 - ERROR - Unexpected error in main: 'NoneType' object has no attribute 'info'
Traceback (most recent call last):
  File "E:\VSCODE\nexust\main.py", line 1041, in main
    browser_session = BrowserSession()
                      ^^^^^^^^^^^^^^^^
  File "E:\VSCODE\nexust\main.py", line 97, in __init__
    logger.info(f"Browser session initialized: {self.browser_type} (Session ID: {self.session_id})")
    ^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'info'
